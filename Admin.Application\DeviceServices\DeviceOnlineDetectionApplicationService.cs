// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.DeviceOnline.Services;
using Admin.Multiplex.Contracts.Consts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;

namespace Admin.Application.DeviceServices;

/// <summary>
/// 设备在线检测应用服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.Device)]
public class DeviceOnlineDetectionApplicationService(
    DeviceOnlineDetectionService detectionService,
    ILogger<DeviceOnlineDetectionApplicationService> logger) : ApplicationService
{
    private readonly DeviceOnlineDetectionService _detectionService = detectionService;
    private readonly ILogger<DeviceOnlineDetectionApplicationService> _logger = logger;

    /// <summary>
    /// 手动执行设备在线检测
    /// </summary>
    /// <param name="input">检测配置输入</param>
    /// <returns>检测结果</returns>
    public async Task<DeviceOnlineDetectionResultOutput> ExecuteDetectionAsync(DeviceOnlineDetectionInput? input = null)
    {
        try
        {
            _logger.LogInformation("开始手动执行设备在线检测");

            // 构建检测配置
            var config = new DeviceOnlineDetectionService.DetectionConfig();
            if (input != null)
            {
                config.KeepAliveTimeoutSeconds = input.KeepAliveTimeoutSeconds ?? config.KeepAliveTimeoutSeconds;
                config.DataReportTimeoutMinutes = input.DataReportTimeoutMinutes ?? config.DataReportTimeoutMinutes;
                config.CheckIntervalSeconds = input.CheckIntervalSeconds ?? config.CheckIntervalSeconds;
            }

            // 执行检测
            var detectionResults = await _detectionService.ExecuteDetectionAsync(config);

            // 处理检测结果
            await _detectionService.ProcessDetectionResultsAsync(detectionResults);

            // 统计结果
            var statistics = CalculateStatistics(detectionResults);

            _logger.LogInformation("手动设备在线检测完成: 总数={Total}, 在线={Online}, 离线={Offline}, " +
                "连接无数据={ConnectedNoData}, 未知={Unknown}",
                statistics.Total, statistics.Online, statistics.Offline, 
                statistics.ConnectedNoData, statistics.Unknown);

            return new DeviceOnlineDetectionResultOutput
            {
                Success = true,
                Message = "设备在线检测执行成功",
                ExecutionTime = DateTime.Now,
                Statistics = statistics,
                DeviceResults = detectionResults.Select(r => new DeviceDetectionResultOutput
                {
                    DeviceId = r.DeviceId,
                    DeviceCode = r.DeviceCode,
                    DeviceName = r.DeviceName,
                    Status = r.Status.ToString(),
                    StatusDescription = r.StatusDescription,
                    MqttConnected = r.MqttConnected,
                    DataReportNormal = r.DataReportNormal,
                    LastKeepAlive = r.LastKeepAlive,
                    LastDataReport = r.LastDataReport,
                    NeedStatusUpdate = r.NeedStatusUpdate,
                    NeedAlarmProcessing = r.NeedAlarmProcessing
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动执行设备在线检测时发生异常");
            return new DeviceOnlineDetectionResultOutput
            {
                Success = false,
                Message = $"设备在线检测执行失败: {ex.Message}",
                ExecutionTime = DateTime.Now,
                Statistics = new DetectionStatisticsOutput(),
                DeviceResults = new List<DeviceDetectionResultOutput>()
            };
        }
    }

    /// <summary>
    /// 获取设备在线状态概览
    /// </summary>
    /// <returns>状态概览</returns>
    public async Task<DeviceOnlineOverviewOutput> GetOnlineOverviewAsync()
    {
        try
        {
            // 执行快速检测（不处理结果，只获取状态）
            var config = new DeviceOnlineDetectionService.DetectionConfig();
            var detectionResults = await _detectionService.ExecuteDetectionAsync(config);

            var statistics = CalculateStatistics(detectionResults);

            var offlineDevices = detectionResults
                .Where(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Disconnected)
                .Select(r => new OfflineDeviceInfo
                {
                    DeviceId = r.DeviceId,
                    DeviceCode = r.DeviceCode,
                    DeviceName = r.DeviceName,
                    LastDataReport = r.LastDataReport,
                    StatusDescription = r.StatusDescription
                })
                .ToList();

            var connectedNoDataDevices = detectionResults
                .Where(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Connected_NoData)
                .Select(r => new ConnectedNoDataDeviceInfo
                {
                    DeviceId = r.DeviceId,
                    DeviceCode = r.DeviceCode,
                    DeviceName = r.DeviceName,
                    LastDataReport = r.LastDataReport,
                    LastKeepAlive = r.LastKeepAlive
                })
                .ToList();

            return new DeviceOnlineOverviewOutput
            {
                Statistics = statistics,
                OfflineDevices = offlineDevices,
                ConnectedNoDataDevices = connectedNoDataDevices,
                LastCheckTime = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备在线状态概览时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 计算检测统计信息
    /// </summary>
    /// <param name="results">检测结果列表</param>
    /// <returns>统计信息</returns>
    private static DetectionStatisticsOutput CalculateStatistics(List<DeviceOnlineDetectionService.DeviceOnlineDetectionResult> results)
    {
        return new DetectionStatisticsOutput
        {
            Total = results.Count,
            Online = results.Count(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Online),
            Offline = results.Count(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Disconnected),
            ConnectedNoData = results.Count(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Connected_NoData),
            Unknown = results.Count(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Unknown),
            OnlineRate = results.Count > 0 ? 
                Math.Round((double)results.Count(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Online) / results.Count * 100, 2) : 0
        };
    }
}

/// <summary>
/// 设备在线检测输入
/// </summary>
public class DeviceOnlineDetectionInput
{
    /// <summary>
    /// Keep-Alive超时时间(秒)
    /// </summary>
    public int? KeepAliveTimeoutSeconds { get; set; }

    /// <summary>
    /// 数据上报超时时间(分钟)
    /// </summary>
    public int? DataReportTimeoutMinutes { get; set; }

    /// <summary>
    /// 检测间隔(秒)
    /// </summary>
    public int? CheckIntervalSeconds { get; set; }
}

/// <summary>
/// 设备在线检测结果输出
/// </summary>
public class DeviceOnlineDetectionResultOutput
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutionTime { get; set; }

    /// <summary>
    /// 统计信息
    /// </summary>
    public DetectionStatisticsOutput Statistics { get; set; }

    /// <summary>
    /// 设备检测结果列表
    /// </summary>
    public List<DeviceDetectionResultOutput> DeviceResults { get; set; }
}

/// <summary>
/// 检测统计信息输出
/// </summary>
public class DetectionStatisticsOutput
{
    /// <summary>
    /// 总设备数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 在线设备数
    /// </summary>
    public int Online { get; set; }

    /// <summary>
    /// 离线设备数
    /// </summary>
    public int Offline { get; set; }

    /// <summary>
    /// 连接但无数据设备数
    /// </summary>
    public int ConnectedNoData { get; set; }

    /// <summary>
    /// 未知状态设备数
    /// </summary>
    public int Unknown { get; set; }

    /// <summary>
    /// 在线率(%)
    /// </summary>
    public double OnlineRate { get; set; }
}

/// <summary>
/// 设备检测结果输出
/// </summary>
public class DeviceDetectionResultOutput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备编码
    /// </summary>
    public string DeviceCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusDescription { get; set; }

    /// <summary>
    /// MQTT是否连接
    /// </summary>
    public bool MqttConnected { get; set; }

    /// <summary>
    /// 数据上报是否正常
    /// </summary>
    public bool DataReportNormal { get; set; }

    /// <summary>
    /// 最后Keep-Alive时间
    /// </summary>
    public DateTime? LastKeepAlive { get; set; }

    /// <summary>
    /// 最后数据上报时间
    /// </summary>
    public DateTime? LastDataReport { get; set; }

    /// <summary>
    /// 是否需要更新状态
    /// </summary>
    public bool NeedStatusUpdate { get; set; }

    /// <summary>
    /// 是否需要告警处理
    /// </summary>
    public bool NeedAlarmProcessing { get; set; }
}

/// <summary>
/// 设备在线状态概览输出
/// </summary>
public class DeviceOnlineOverviewOutput
{
    /// <summary>
    /// 统计信息
    /// </summary>
    public DetectionStatisticsOutput Statistics { get; set; }

    /// <summary>
    /// 离线设备列表
    /// </summary>
    public List<OfflineDeviceInfo> OfflineDevices { get; set; }

    /// <summary>
    /// 连接但无数据的设备列表
    /// </summary>
    public List<ConnectedNoDataDeviceInfo> ConnectedNoDataDevices { get; set; }

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastCheckTime { get; set; }
}

/// <summary>
/// 离线设备信息
/// </summary>
public class OfflineDeviceInfo
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备编码
    /// </summary>
    public string DeviceCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 最后数据上报时间
    /// </summary>
    public DateTime? LastDataReport { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusDescription { get; set; }
}

/// <summary>
/// 连接但无数据的设备信息
/// </summary>
public class ConnectedNoDataDeviceInfo
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备编码
    /// </summary>
    public string DeviceCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 最后数据上报时间
    /// </summary>
    public DateTime? LastDataReport { get; set; }

    /// <summary>
    /// 最后Keep-Alive时间
    /// </summary>
    public DateTime? LastKeepAlive { get; set; }
}
