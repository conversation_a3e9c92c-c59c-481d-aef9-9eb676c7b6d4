[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[16:07:53] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[16:07:53] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[16:07:53] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[16:07:53] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[16:07:54] [INF] Admin.Communication.AdminCommunicationModule 
MQTT连接事件处理器注册成功

[16:07:54] [INF]  
项目当前环境为：Development

[16:07:54] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[16:07:54] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[16:07:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[16:07:56] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:07:56] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:52022

[16:07:56] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=123123

[16:07:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

