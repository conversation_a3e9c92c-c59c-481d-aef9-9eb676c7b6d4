// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.DeviceOnline.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Admin.Communication.DeviceOnline.Workers;

/// <summary>
/// 设备在线检测后台工作器
/// 负责定期检测直连设备和网关设备的在线状态，并处理相关告警和状态同步
/// </summary>
public class DeviceOnlineDetectionWorker : AsyncPeriodicBackgroundWorkerBase
{
    private readonly ILogger<DeviceOnlineDetectionWorker> _logger;
    private readonly IConfiguration _configuration;
    private readonly DeviceOnlineDetectionService.DetectionConfig _detectionConfig;

    public DeviceOnlineDetectionWorker(
        AbpAsyncTimer timer,
        IServiceScopeFactory serviceScopeFactory,
        IConfiguration configuration,
        ILogger<DeviceOnlineDetectionWorker> logger)
        : base(timer, serviceScopeFactory)
    {
        _logger = logger;
        _configuration = configuration;

        // 从配置文件读取检测参数
        _detectionConfig = new DeviceOnlineDetectionService.DetectionConfig
        {
            CheckIntervalSeconds = _configuration.GetValue<int>("DeviceOnlineDetection:CheckInterval", 30)
        };

        // 设置工作器执行间隔
        Timer.Period = _detectionConfig.CheckIntervalSeconds * 1000; // 转换为毫秒

        _logger.LogInformation("设备在线检测工作器初始化完成: CheckInterval={CheckInterval}s",
            _detectionConfig.CheckIntervalSeconds);
    }

    /// <summary>
    /// 执行设备在线检测任务
    /// </summary>
    /// <param name="workerContext">工作器上下文</param>
    /// <returns>执行任务</returns>
    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        try
        {
            using var scope = workerContext.ServiceProvider.CreateScope();
            var detectionService = scope.ServiceProvider.GetRequiredService<DeviceOnlineDetectionService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<DeviceOnlineDetectionWorker>>();

            logger.LogDebug("开始执行设备在线检测任务");

            // 1. 执行设备在线检测
            var detectionResults = await detectionService.ExecuteDetectionAsync(
                _detectionConfig, 
                workerContext.CancellationToken);

            if (detectionResults.Count == 0)
            {
                logger.LogDebug("没有需要检测的设备");
                return;
            }

            // 2. 统计检测结果
            var statistics = CalculateStatistics(detectionResults);
            logger.LogInformation("设备在线检测完成: 总数={Total}, 在线={Online}, 离线={Offline}",
                statistics.Total, statistics.Online, statistics.Offline);

            // 3. 处理检测结果 - 更新设备状态和处理告警
            await detectionService.ProcessDetectionResultsAsync(
                detectionResults, 
                workerContext.CancellationToken);

            // 4. 记录需要处理的设备
            LogDevicesNeedingAttention(detectionResults, logger);

            logger.LogDebug("设备在线检测任务执行完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行设备在线检测任务时发生异常");
        }
    }

    /// <summary>
    /// 计算检测统计信息
    /// </summary>
    /// <param name="results">检测结果列表</param>
    /// <returns>统计信息</returns>
    private static DetectionStatistics CalculateStatistics(List<DeviceOnlineDetectionService.DeviceOnlineDetectionResult> results)
    {
        var statistics = new DetectionStatistics
        {
            Total = results.Count,
            Online = results.Count(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Online),
            Offline = results.Count(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Offline)
        };

        return statistics;
    }

    /// <summary>
    /// 记录需要关注的设备
    /// </summary>
    /// <param name="results">检测结果列表</param>
    /// <param name="logger">日志记录器</param>
    private static void LogDevicesNeedingAttention(
        List<DeviceOnlineDetectionService.DeviceOnlineDetectionResult> results, 
        ILogger logger)
    {
        // 记录离线设备
        var offlineDevices = results
            .Where(r => r.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Offline)
            .ToList();

        if (offlineDevices.Count > 0)
        {
            logger.LogWarning("发现 {Count} 个离线设备: {Devices}",
                offlineDevices.Count,
                string.Join(", ", offlineDevices.Select(d => $"{d.DeviceName}({d.DeviceCode})")));
        }

        // 记录状态更新的设备
        var statusUpdatedDevices = results
            .Where(r => r.NeedStatusUpdate)
            .ToList();

        if (statusUpdatedDevices.Count > 0)
        {
            logger.LogInformation("更新了 {Count} 个设备的状态: {Devices}",
                statusUpdatedDevices.Count,
                string.Join(", ", statusUpdatedDevices.Select(d => 
                    $"{d.DeviceName}({d.DeviceCode}): {d.OriginalStatus} -> {(d.Status == DeviceOnlineDetectionService.DeviceConnectionStatus.Online ? 1 : 0)}")));
        }

        // 记录需要告警处理的设备
        var alarmProcessingDevices = results
            .Where(r => r.NeedAlarmProcessing)
            .ToList();

        if (alarmProcessingDevices.Count > 0)
        {
            logger.LogInformation("处理了 {Count} 个设备的通讯告警: {Devices}",
                alarmProcessingDevices.Count,
                string.Join(", ", alarmProcessingDevices.Select(d => $"{d.DeviceName}({d.DeviceCode})")));
        }
    }

    /// <summary>
    /// 检测统计信息
    /// </summary>
    private class DetectionStatistics
    {
        /// <summary>
        /// 总设备数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 在线设备数
        /// </summary>
        public int Online { get; set; }

        /// <summary>
        /// 离线设备数
        /// </summary>
        public int Offline { get; set; }
    }
}
