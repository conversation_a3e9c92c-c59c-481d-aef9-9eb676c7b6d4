// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Alarm.Models;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Volo.Abp.DependencyInjection;

namespace Admin.Communication.Alarm.Services;

/// <summary>
/// 告警服务
/// </summary>
public class AlarmService : ITransientDependency
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<AlarmService> _logger;

    public AlarmService(ISqlSugarClient db, ILogger<AlarmService> logger)
    {
        _db = db;
        _logger = logger;
    }

    /// <summary>
    /// 处理参数告警（生成或更新告警事件）
    /// 告警事件周期：事件产生 → 事件解除 → 才能对同一事件进行新的告警
    /// </summary>
    public async Task ProcessParameterAlarmAsync(
        AlarmDetectionResult alarmResult,
        DeviceEntity device,
        DeviceParaEntity parameter)
    {
        try
        {
            // 生成事件ID
            var eventId = GenerateEventId(alarmResult, device, parameter);

            // 检查是否存在未解除的同类型告警
            var existingAlarm = await GetActiveAlarmByEventIdAsync(eventId);

            if (existingAlarm != null)
            {
                // 如果存在未解除的告警，不创建新记录，只记录日志
                _logger.LogDebug("告警事件已存在且未解除，跳过创建: EventId={EventId}, AlarmId={AlarmId}",
                    eventId, existingAlarm.Id);
                return;
            }

            // 只有在没有未解除的告警时，才创建新的告警记录
            await CreateParameterAlarmAsync(device, parameter, alarmResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理参数告警时发生异常: DeviceId={DeviceId}, ParameterId={ParameterId}",
                device.Id, parameter.ParameterId);
        }
    }

    /// <summary>
    /// 处理通讯失败告警
    /// 告警事件周期：事件产生 → 事件解除 → 才能对同一事件进行新的告警
    /// </summary>
    public async Task ProcessCommunicationFailureAlarmAsync(DeviceEntity device, string reason = "通讯失败")
    {
        try
        {
            var eventId = $"COMM_FAIL_{device.Id}";

            // 检查是否存在未解除的通讯失败告警
            var existingAlarm = await GetActiveAlarmByEventIdAsync(eventId);

            if (existingAlarm != null)
            {
                // 如果存在未解除的告警，不创建新记录，只记录日志
                _logger.LogDebug("通讯失败告警已存在且未解除，跳过创建: EventId={EventId}, AlarmId={AlarmId}",
                    eventId, existingAlarm.Id);
                return;
            }

            // 只有在没有未解除的告警时，才创建新的告警记录
            var alarmHistory = new AlarmHistoryEntity
            {
                EventId = eventId,
                DeviceId = device.Id,
                AlarmDescription = GenerateCommunicationFailureTitle(device),
                AlarmValue = 0, // 通讯失败没有具体数值
                AlarmStatus = (int)AlarmStatusEnum.PendingConfirmation,
                AlarmTime = DateTime.Now
            };

            await _db.Insertable(alarmHistory).ExecuteCommandAsync();

            _logger.LogInformation("创建通讯失败告警: DeviceId={DeviceId}, DeviceName={DeviceName}",
                device.Id, device.DeviceName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理通讯失败告警时发生异常: DeviceId={DeviceId}", device.Id);
        }
    }

    /// <summary>
    /// 检查通讯告警是否需要清除
    /// </summary>
    public async Task CheckCommunicationAlarmClearAsync(long deviceId)
    {
        try
        {
            var eventId = $"COMM_FAIL_{deviceId}";

            // 获取该设备的未解除通讯告警
            var activeAlarms = await _db.Queryable<AlarmHistoryEntity>()
                .Where(x => x.EventId == eventId && x.IsReleased == 0)
                .ToListAsync();

            if (activeAlarms.Count == 0)
            {
                _logger.LogDebug("设备无需清除的通讯告警: DeviceId={DeviceId}", deviceId);
                return;
            }

            // 清除所有未解除的通讯告警
            foreach (var alarm in activeAlarms)
            {
                alarm.IsReleased = 1;
                alarm.ReleaseTime = DateTime.Now;
                alarm.ReleaseDescription = "设备通讯恢复正常";
            }

            var updateCount = await _db.Updateable(activeAlarms).ExecuteCommandAsync();

            _logger.LogInformation("清除通讯告警成功: DeviceId={DeviceId}, 清除数量={Count}",
                deviceId, updateCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查通讯告警清除时发生异常: DeviceId={DeviceId}", deviceId);
        }
    }

    /// <summary>
    /// 检查参数告警是否需要清除
    /// </summary>
    public async Task CheckParameterAlarmClearAsync(long deviceId, long parameterId, decimal currentValue, DeviceParaEntity parameter)
    {
        try
        {
            // 获取该参数的所有未解除告警
            var activeAlarms = await _db.Queryable<AlarmHistoryEntity>()
                .Where(a => a.DeviceId == deviceId &&
                           a.EventId.Contains($"_{parameterId}") &&
                           a.IsReleased == 0)
                .ToListAsync();

            foreach (var alarm in activeAlarms)
            {
                bool shouldClear = ShouldClearParameterAlarm(parameter, currentValue, alarm);

                if (shouldClear)
                {
                    // 清除告警
                    alarm.IsReleased = 1;
                    alarm.ReleaseValue = currentValue;
                    alarm.ReleaseTime = DateTime.Now;
                    alarm.ReleaseDescription = "参数值恢复正常";
                    await _db.Updateable(alarm).ExecuteCommandAsync();

                    _logger.LogInformation("清除参数告警: DeviceId={DeviceId}, ParameterId={ParameterId}, AlarmId={AlarmId}", 
                        deviceId, parameterId, alarm.Id);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查参数告警清除时发生异常: DeviceId={DeviceId}, ParameterId={ParameterId}", 
                deviceId, parameterId);
        }
    }

    /// <summary>
    /// 清除通讯失败告警
    /// </summary>
    public async Task ClearCommunicationFailureAlarmAsync(long deviceId)
    {
        try
        {
            var communicationAlarms = await _db.Queryable<AlarmHistoryEntity>()
                .Where(a => a.DeviceId == deviceId &&
                           a.EventId == $"COMM_FAIL_{deviceId}" &&
                           a.ReleaseTime == null)
                .ToListAsync();

            foreach (var alarm in communicationAlarms)
            {
                alarm.ReleaseTime = DateTime.Now;
                alarm.ReleaseDescription = "通讯恢复正常";
                await _db.Updateable(alarm).ExecuteCommandAsync();
                
                _logger.LogInformation("清除通讯失败告警: DeviceId={DeviceId}, AlarmId={AlarmId}", 
                    deviceId, alarm.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除通讯失败告警时发生异常: DeviceId={DeviceId}", deviceId);
        }
    }



    /// <summary>
    /// 创建参数告警
    /// </summary>
    private async Task CreateParameterAlarmAsync(DeviceEntity device, DeviceParaEntity parameter, AlarmDetectionResult alarmResult)
    {
        // 生成事件ID
        var eventId = GenerateEventId(alarmResult, device, parameter);

        var alarmHistory = new AlarmHistoryEntity
        {
            EventId = eventId,
            DeviceId = device.Id,
            AlarmDescription = GenerateParameterAlarmTitle(device, parameter, alarmResult),
            AlarmValue = alarmResult.CurrentValue ?? 0,
            AlarmStatus = (int)AlarmStatusEnum.PendingConfirmation,
            AlarmTime = DateTime.Now
        };

        await _db.Insertable(alarmHistory).ExecuteCommandAsync();

        _logger.LogInformation("创建参数告警: DeviceId={DeviceId}, Parameter={ParameterName}, Level={Level}, Value={Value}",
            device.Id, parameter.Name, alarmResult.AlarmLevel, alarmResult.CurrentValue);
    }

    /// <summary>
    /// 获取指定事件ID的未解除告警
    /// </summary>
    private async Task<AlarmHistoryEntity?> GetActiveAlarmByEventIdAsync(string eventId)
    {
        return await _db.Queryable<AlarmHistoryEntity>()
            .Where(a => a.EventId == eventId && a.IsReleased == 0)
            .FirstAsync();
    }

    /// <summary>
    /// 生成事件ID
    /// </summary>
    private string GenerateEventId(AlarmDetectionResult alarmResult, DeviceEntity device, DeviceParaEntity parameter)
    {
        // 根据告警类型和等级生成精确的事件ID
        return alarmResult.AlarmType switch
        {
            AlarmTypeEnum.CommunicationFailure => $"C_F_{device.Id}",
            AlarmTypeEnum.ValueOverUpperLimit => alarmResult.AlarmLevel == AlarmLevelEnum.Warning
                ? $"U_W_{parameter.ParameterId}"
                : $"U_L_{parameter.ParameterId}",
            AlarmTypeEnum.ValueOverLowerLimit => alarmResult.AlarmLevel == AlarmLevelEnum.Warning
                ? $"L_W_{parameter.ParameterId}"
                : $"L_L_{parameter.ParameterId}",
            AlarmTypeEnum.StatusAbnormal => $"S_A_{parameter.ParameterId}",
            _ => throw new ArgumentException($"未知的告警类型: {alarmResult.AlarmType}")
        };
    }

    /// <summary>
    /// 生成通讯失败告警标题
    /// </summary>
    private string GenerateCommunicationFailureTitle(DeviceEntity device)
    {
        return $"{device.DeviceName} 通讯失败";
    }

    /// <summary>
    /// 生成参数告警标题
    /// </summary>
    private string GenerateParameterAlarmTitle(DeviceEntity device, DeviceParaEntity parameter, AlarmDetectionResult alarmResult)
    {
        var limitType = alarmResult.IsUpperLimit ? "上限" : "下限";
        var alarmType = alarmResult.AlarmLevel == AlarmLevelEnum.Warning ? "预警" : "告警";
        return $"{device.DeviceName} {parameter.Name} {limitType}{alarmType}";
    }

    /// <summary>
    /// 判断参数告警是否应该清除
    /// </summary>
    private bool ShouldClearParameterAlarm(DeviceParaEntity parameter, decimal currentValue, AlarmHistoryEntity alarm)
    {
        // 根据事件ID判断告警类型和清除条件
        if (alarm.EventId.StartsWith("UPPER_LIMIT_"))
        {
            // 上限告警：当前值小于等于清除阈值时清除
            if (parameter.AlarmUpperLimit.HasValue && currentValue <= parameter.AlarmUpperLimitClearValue)
            {
                return true;
            }
            if (parameter.WarningUpperLimit.HasValue && currentValue <= parameter.WarningUpperLimitClearValue)
            {
                return true;
            }
        }
        else if (alarm.EventId.StartsWith("LOWER_LIMIT_"))
        {
            // 下限告警：当前值大于等于清除阈值时清除
            if (parameter.AlarmLowerLimit.HasValue && currentValue >= parameter.AlarmLowerLimitClearValue)
            {
                return true;
            }
            if (parameter.WarningLowerLimit.HasValue && currentValue >= parameter.WarningLowerLimitClearValue)
            {
                return true;
            }
        }

        return false;
    }


}
