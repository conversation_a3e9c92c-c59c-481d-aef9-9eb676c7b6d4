# 设备在线检测功能

## 功能概述

设备在线检测功能负责监控直连设备和网关设备的在线状态，通过双重检测机制确保设备状态的准确性，并自动处理相关的告警和状态同步。

## 检测策略

### 双重检测机制

1. **Keep-Alive检测** (30-45秒)
   - 基于MQTT协议的Keep-Alive心跳机制
   - 客户端每30秒发送PINGREQ，服务器响应PINGRESP
   - 超过45秒未收到心跳则判定为连接断开
   - 适合检测网络故障、设备断电等硬件问题

2. **数据上报检测** (60-120秒)
   - 基于设备业务数据的上报频率
   - 设备数据上报周期≤60秒
   - 超过2分钟未收到数据则判定为数据异常
   - 适合检测传感器故障、数据采集异常等软件问题

### 设备状态分类

- **Online**: MQTT连接正常 + 数据上报正常
- **Connected_NoData**: MQTT连接正常 + 数据上报超时
- **Disconnected**: MQTT连接断开
- **Unknown**: 初始状态或异常状态

## 配置参数

```json
{
  "DeviceOnlineDetection": {
    "KeepAliveTimeout": 45,      // Keep-Alive超时时间(秒)
    "DataReportTimeout": 2,      // 数据上报超时时间(分钟)
    "CheckInterval": 30          // 检测任务执行间隔(秒)
  }
}
```

## 核心组件

### DeviceOnlineDetectionService

设备在线检测服务，负责执行检测逻辑和处理检测结果。

**主要方法：**
- `ExecuteDetectionAsync()`: 执行设备在线检测
- `ProcessDetectionResultsAsync()`: 处理检测结果，更新设备状态和处理告警

### DeviceOnlineDetectionWorker

设备在线检测后台工作器，定期执行检测任务。

**功能：**
- 定期执行设备在线检测（默认30秒间隔）
- 统计检测结果
- 记录需要关注的设备

## 联动功能

### 设备状态同步

检测到设备状态变化时，自动更新数据库中的设备状态：
- 在线设备：`Status = 1`
- 离线设备：`Status = 0`

### 通讯告警处理

根据检测结果自动处理通讯告警：

1. **设备离线时**：
   - 创建通讯失败告警
   - 告警类型：`CommunicationFailure`
   - 告警状态：`PendingConfirmation`

2. **设备恢复在线时**：
   - 自动清除通讯失败告警
   - 设置解除状态：`IsReleased = 1`
   - 记录解除时间和原因

## 使用示例

### 手动执行检测

```csharp
// 注入服务
var detectionService = serviceProvider.GetRequiredService<DeviceOnlineDetectionService>();

// 执行检测
var config = new DeviceOnlineDetectionService.DetectionConfig
{
    KeepAliveTimeoutSeconds = 45,
    DataReportTimeoutMinutes = 2,
    CheckIntervalSeconds = 30
};

var results = await detectionService.ExecuteDetectionAsync(config);

// 处理结果
await detectionService.ProcessDetectionResultsAsync(results);
```

### 查看检测统计

检测完成后会输出统计信息：
```
设备在线检测完成: 总数=50, 在线=45, 离线=3, 连接无数据=2, 未知=0
```

### 查看需要关注的设备

系统会自动记录需要关注的设备：
```
发现 3 个离线设备: 温度传感器01(temp001), 湿度传感器02(humi002), 网关设备03(gateway003)
发现 2 个连接但无数据的设备: 压力传感器04(press004), 流量计05(flow005)
```

## 监控和日志

### 日志级别

- **Debug**: 详细的检测过程信息
- **Information**: 检测结果统计和状态更新
- **Warning**: 发现离线或异常设备
- **Error**: 检测过程中的异常

### 关键日志示例

```
[Information] 设备在线检测工作器初始化完成: KeepAliveTimeout=45s, DataReportTimeout=2min, CheckInterval=30s
[Information] 设备在线检测完成: 总数=50, 在线=45, 离线=3, 连接无数据=2, 未知=0
[Warning] 发现 3 个离线设备: 温度传感器01(temp001), 湿度传感器02(humi002)
[Information] 更新了 5 个设备的状态: 温度传感器01(temp001): 1 -> 0
[Information] 处理了 3 个设备的通讯告警: 温度传感器01(temp001), 湿度传感器02(humi002)
```

## 性能考虑

1. **批量处理**: 一次检测所有设备，避免频繁的数据库查询
2. **异步执行**: 所有数据库操作都是异步的
3. **错误隔离**: 单个设备检测失败不影响其他设备
4. **资源控制**: 通过配置控制检测频率，避免过度消耗资源

## 扩展性

该架构支持以下扩展：

1. **自定义检测策略**: 可以为不同类型的设备配置不同的检测参数
2. **多级告警**: 支持不同严重程度的离线告警
3. **通知集成**: 可以集成邮件、短信等通知方式
4. **历史记录**: 可以记录设备在线状态的历史变化
5. **性能监控**: 可以添加检测性能的监控指标

## 故障排除

### 常见问题

1. **设备误报离线**
   - 检查Keep-Alive和数据上报的超时配置
   - 确认网络连接稳定性
   - 查看设备端的心跳和数据上报实现

2. **检测延迟过大**
   - 调整检测间隔配置
   - 检查数据库性能
   - 优化检测逻辑

3. **告警处理异常**
   - 检查告警服务的配置
   - 确认数据库中告警相关表的结构
   - 查看告警处理的日志

### 调试建议

1. 启用Debug日志级别查看详细信息
2. 监控检测任务的执行时间
3. 检查数据库中设备状态和告警记录的变化
4. 使用MQTT客户端工具验证连接状态
